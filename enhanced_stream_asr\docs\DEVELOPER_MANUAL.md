# Enhanced Stream ASR 开发者手册

## 📖 目录

1. [项目概述](#项目概述)
2. [架构设计](#架构设计)
3. [开发环境搭建](#开发环境搭建)
4. [代码结构](#代码结构)
5. [核心模块详解](#核心模块详解)
6. [API设计](#api设计)
7. [数据流程](#数据流程)
8. [扩展开发](#扩展开发)
9. [测试指南](#测试指南)
10. [部署指南](#部署指南)
11. [性能优化](#性能优化)
12. [故障排查](#故障排查)

---

## 🎯 项目概述

Enhanced Stream ASR 是一个企业级的实时语音识别系统，采用现代化的微服务架构设计，支持高并发、低延迟的语音识别服务。

### 技术栈

- **后端框架**: FastAPI + Uvicorn
- **WebSocket**: FastAPI WebSocket
- **深度学习**: PyTorch + ONNX Runtime
- **音频处理**: WebRTC VAD + NumPy
- **配置管理**: YAML + Pydantic
- **监控告警**: 自研监控系统
- **前端**: 原生JavaScript + Chart.js

### 设计原则

1. **高性能**: 优化的推理引擎和内存管理
2. **高可用**: 完善的错误处理和恢复机制
3. **可扩展**: 模块化设计，易于扩展新功能
4. **可维护**: 清晰的代码结构和完善的文档
5. **可监控**: 全面的性能监控和告警系统

---

## 🏗️ 架构设计

### 整体架构

#### 系统架构图

```mermaid
graph TB
    subgraph "客户端层"
        WEB[Web浏览器]
        MOBILE[移动应用]
        API_CLIENT[第三方客户端]
    end

    subgraph "API网关层"
        HTTP_API[HTTP API]
        WS_API[WebSocket API]
        MONITOR_API[监控API]
    end

    subgraph "业务逻辑层"
        SESSION_MGR[会话管理器]
        PROTOCOL_HANDLER[协议处理器]
        ERROR_HANDLER[错误处理器]
    end

    subgraph "核心引擎层"
        VAD[VAD处理器]
        LID[语种识别引擎]
        ASR[ASR引擎]
        TEXT_PROC[文本处理器]
    end

    subgraph "推理引擎层"
        ONNX_POOL[ONNX引擎池]
        MODEL_MGR[模型管理器]
        CACHE_MGR[缓存管理器]
    end

    subgraph "基础设施层"
        CONFIG_MGR[配置管理器]
        LOG_SYS[日志系统]
        MONITOR_SYS[监控系统]
        STORAGE[存储系统]
    end

    %% 连接关系
    WEB --> WS_API
    MOBILE --> WS_API
    API_CLIENT --> HTTP_API

    HTTP_API --> SESSION_MGR
    WS_API --> PROTOCOL_HANDLER
    MONITOR_API --> MONITOR_SYS

    PROTOCOL_HANDLER --> SESSION_MGR
    SESSION_MGR --> ERROR_HANDLER

    SESSION_MGR --> VAD
    VAD --> LID
    LID --> ASR
    ASR --> TEXT_PROC

    VAD --> ONNX_POOL
    LID --> ONNX_POOL
    ASR --> ONNX_POOL

    ONNX_POOL --> MODEL_MGR
    ONNX_POOL --> CACHE_MGR

    SESSION_MGR --> CONFIG_MGR
    ERROR_HANDLER --> LOG_SYS
    SESSION_MGR --> MONITOR_SYS
    MODEL_MGR --> STORAGE

    %% 样式
    classDef clientLayer fill:#e1f5fe
    classDef apiLayer fill:#f3e5f5
    classDef businessLayer fill:#e8f5e8
    classDef engineLayer fill:#fff3e0
    classDef infraLayer fill:#fce4ec

    class WEB,MOBILE,API_CLIENT clientLayer
    class HTTP_API,WS_API,MONITOR_API apiLayer
    class SESSION_MGR,PROTOCOL_HANDLER,ERROR_HANDLER businessLayer
    class VAD,LID,ASR,TEXT_PROC engineLayer
    class CONFIG_MGR,LOG_SYS,MONITOR_SYS,STORAGE infraLayer
```

### 核心组件

#### 1. 会话管理器 (Session Manager)
- 管理WebSocket连接和会话生命周期
- 处理并发连接和资源分配
- 实现会话状态机和超时管理

#### 2. 协议处理器 (Protocol Handler)
- 处理WebSocket消息协议
- 实现三阶段通信协议（握手、数据传输、断开）
- 消息序列化和反序列化

#### 3. 音频处理引擎
- **VAD处理器**: 语音活动检测和音频分段
- **LID引擎**: 语种识别和渐进式检测
- **ASR引擎**: 流式语音识别和文本后处理

#### 4. ONNX引擎池
- 模型加载和推理管理
- 动态扩缩容和负载均衡
- 内存优化和资源回收

---

## 🛠️ 开发环境搭建

### 1. 环境要求

```bash
# Python版本
python --version  # >= 3.8

# 系统依赖 (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    python3-dev \
    libffi-dev \
    libssl-dev \
    libasound2-dev \
    portaudio19-dev

# 系统依赖 (CentOS/RHEL)
sudo yum groupinstall -y "Development Tools"
sudo yum install -y \
    python3-devel \
    libffi-devel \
    openssl-devel \
    alsa-lib-devel \
    portaudio-devel
```

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd enhanced_stream_asr

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行测试确保环境正常
python -m pytest tests/
```

### 3. IDE配置

#### VS Code配置 (`.vscode/settings.json`)

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### PyCharm配置
1. 设置Python解释器为虚拟环境
2. 启用代码格式化工具（Black）
3. 配置代码检查工具（Pylint, Flake8）
4. 设置测试运行器为pytest

---

## 📁 代码结构

```
enhanced_stream_asr/
├── api/                    # API接口层
│   ├── http/              # HTTP API
│   ├── websocket/         # WebSocket API
│   └── monitoring/        # 监控API
├── core/                  # 核心业务逻辑
│   ├── session/           # 会话管理
│   ├── audio/             # 音频处理
│   ├── lid/               # 语种识别
│   ├── asr/               # 语音识别
│   └── engines/           # 推理引擎
├── utils/                 # 工具模块
│   ├── config/            # 配置管理
│   ├── monitoring/        # 监控系统
│   ├── exceptions.py      # 异常处理
│   └── logger.py          # 日志系统
├── web/                   # Web前端
│   └── static/            # 静态文件
├── configs/               # 配置文件
│   ├── server_config.yaml
│   ├── lid_config.yaml
│   └── lang_configs/      # 语种配置
├── models/                # 模型文件
├── tests/                 # 测试代码
├── scripts/               # 工具脚本
├── docs/                  # 文档
└── server.py              # 主服务器
```

### 模块职责

| 模块 | 职责 | 主要类/函数 |
|------|------|-------------|
| `api.websocket` | WebSocket协议处理 | `WebSocketHandler`, `WebSocketProtocol` |
| `core.session` | 会话生命周期管理 | `SessionManager`, `Session` |
| `core.audio` | 音频处理和VAD | `VADProcessor`, `FeatureExtractor` |
| `core.lid` | 语种识别 | `LIDEngine`, `ProgressiveLID` |
| `core.asr` | 语音识别 | `StreamingASREngine`, `TextProcessor` |
| `core.engines` | ONNX推理引擎 | `PooledONNXEngine`, `SessionPool` |
| `utils.config` | 配置管理 | `ConfigManager`, `ConfigValidator` |
| `utils.monitoring` | 监控和告警 | `PerformanceMonitor`, `AlertManager` |

---

## 🔧 核心模块详解

### 1. 会话管理器 (SessionManager)

#### 核心功能
- WebSocket连接管理
- 会话状态跟踪
- 资源分配和回收
- 并发控制

#### 关键方法

```python
class SessionManager:
    async def create_session(self, session_id: str, client_id: str, 
                           handshake_req: HandshakeRequest, 
                           websocket: WebSocket) -> bool:
        """创建新会话"""
        
    async def process_audio_data(self, session_id: str, 
                               audio_message: AudioDataMessage) -> Dict[str, Any]:
        """处理音频数据"""
        
    async def cleanup_session(self, session_id: str):
        """清理会话资源"""
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
```

#### 状态机设计

```python
class SessionState(Enum):
    INITIALIZING = "initializing"      # 初始化中
    WAITING_FOR_SPEECH = "waiting"     # 等待语音
    DETECTING_LANGUAGE = "detecting"   # 检测语种
    PROCESSING_SPEECH = "processing"   # 处理语音
    ERROR = "error"                    # 错误状态
    TERMINATED = "terminated"          # 已终止
```

### 2. 流式ASR引擎 (StreamingASREngine)

#### 核心算法
- 编码器-CTC架构
- 流式特征提取
- 实时解码和后处理

#### 关键方法

```python
class StreamingASREngine:
    async def process_audio_chunk(self, audio_data: Union[bytes, np.ndarray], 
                                 is_final: bool = False) -> Dict[str, Any]:
        """处理音频块"""
        
    async def _run_encoder(self, chunk: torch.Tensor) -> Optional[torch.Tensor]:
        """运行编码器推理"""
        
    def _ctc_prefix_beam_search(self, ctc_probs: torch.Tensor, 
                               beam_size: int = 10) -> Tuple[List[int], List[float]]:
        """CTC前缀束搜索解码"""
```

#### 性能优化
- 滑动窗口缓存机制
- 批处理推理
- 内存池管理

### 3. ONNX引擎池 (PooledONNXEngine)

#### 设计目标
- 支持高并发推理
- 动态扩缩容
- 内存优化

#### 实现原理

```python
class PooledONNXEngine:
    def __init__(self, model_path: str, config: Dict[str, Any]):
        self.session_pool = SessionPool(
            model_path=model_path,
            initial_size=config.get('initial_pool_size', 2),
            max_size=config.get('max_pool_size', 10),
            providers=config.get('providers', ['CPUExecutionProvider'])
        )
    
    async def infer(self, inputs: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """执行推理"""
        async with self.session_pool.get_session() as session:
            return await self._run_inference(session, inputs)
```

---

## 🌐 API设计

### WebSocket API详细文档

#### 完整使用示例

##### JavaScript客户端示例

```javascript
// websocket_client.js
class ASRWebSocketClient {
    constructor(serverUrl = 'ws://localhost:8080/ws/stream') {
        this.serverUrl = serverUrl;
        this.websocket = null;
        this.sessionId = null;
        this.sequenceId = 0;
        this.isRecording = false;

        // 事件回调
        this.onConnected = null;
        this.onResult = null;
        this.onError = null;
        this.onDisconnected = null;
    }

    async connect(config = {}) {
        return new Promise((resolve, reject) => {
            try {
                this.websocket = new WebSocket(this.serverUrl);

                this.websocket.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.sendHandshake(config);
                };

                this.websocket.onmessage = (event) => {
                    this.handleMessage(JSON.parse(event.data));
                };

                this.websocket.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    if (this.onError) this.onError(error);
                    reject(error);
                };

                this.websocket.onclose = (event) => {
                    console.log('WebSocket连接已关闭:', event.code, event.reason);
                    if (this.onDisconnected) this.onDisconnected(event);
                };

                // 设置连接超时
                setTimeout(() => {
                    if (this.websocket.readyState !== WebSocket.OPEN) {
                        reject(new Error('连接超时'));
                    }
                }, 10000);

            } catch (error) {
                reject(error);
            }
        });
    }

    sendHandshake(config) {
        const handshakeRequest = {
            type: 'handshake_request',
            client_id: config.clientId || this.generateClientId(),
            language: config.language || null,
            auto_language_detection: config.autoLanguageDetection !== false,
            sample_rate: config.sampleRate || 16000,
            channels: config.channels || 1,
            sample_width: config.sampleWidth || 2,
            chunk_duration: config.chunkDuration || 0.4,
            enable_intermediate_result: config.enableIntermediateResult !== false,
            enable_punctuation: config.enablePunctuation !== false,
            enable_itn: config.enableITN !== false,
            custom_separator: config.customSeparator || null,
            hotwords: config.hotwords || null,
            timestamp: Date.now()
        };

        this.send(handshakeRequest);
    }

    handleMessage(message) {
        console.log('收到消息:', message);

        switch (message.type) {
            case 'handshake_response':
                this.handleHandshakeResponse(message);
                break;
            case 'recognition_result':
            case 'intermediate_result':
                this.handleRecognitionResult(message);
                break;
            case 'status_update':
                this.handleStatusUpdate(message);
                break;
            case 'error':
                this.handleError(message);
                break;
            default:
                console.warn('未知消息类型:', message.type);
        }
    }

    handleHandshakeResponse(message) {
        if (message.status === 'success') {
            this.sessionId = message.session_id;
            console.log('握手成功，会话ID:', this.sessionId);
            if (this.onConnected) {
                this.onConnected({
                    sessionId: this.sessionId,
                    serverConfig: message.server_config
                });
            }
        } else {
            console.error('握手失败:', message.message);
            if (this.onError) {
                this.onError(new Error(message.message));
            }
        }
    }

    handleRecognitionResult(message) {
        if (this.onResult) {
            this.onResult({
                text: message.text,
                isFinal: message.is_final,
                confidence: message.confidence,
                language: message.language,
                processingTime: message.processing_time,
                timestamp: message.timestamp
            });
        }
    }

    handleStatusUpdate(message) {
        console.log('状态更新:', message.state, message.message);
    }

    handleError(message) {
        console.error('服务器错误:', message);
        if (this.onError) {
            this.onError(new Error(`[${message.error_code}] ${message.message}`));
        }
    }

    async startRecording() {
        if (!this.sessionId) {
            throw new Error('会话未建立');
        }

        this.isRecording = true;
        this.sequenceId = 0;

        // 发送开始录音消息
        this.send({
            type: 'start_recording',
            session_id: this.sessionId,
            timestamp: Date.now()
        });

        // 开始音频采集
        await this.startAudioCapture();
    }

    async startAudioCapture() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            const audioContext = new AudioContext({ sampleRate: 16000 });
            const source = audioContext.createMediaStreamSource(stream);
            const processor = audioContext.createScriptProcessor(6400, 1, 1); // 0.4秒@16kHz

            processor.onaudioprocess = (event) => {
                if (this.isRecording) {
                    const audioData = event.inputBuffer.getChannelData(0);
                    this.sendAudioData(audioData);
                }
            };

            source.connect(processor);
            processor.connect(audioContext.destination);

            this.audioContext = audioContext;
            this.audioStream = stream;
            this.audioProcessor = processor;

        } catch (error) {
            console.error('音频采集失败:', error);
            throw error;
        }
    }

    sendAudioData(audioData, isFinal = false) {
        if (!this.sessionId || !this.isRecording) return;

        // 转换为Int16Array
        const int16Array = new Int16Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
            int16Array[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));
        }

        // 转换为Base64
        const audioBase64 = this.arrayBufferToBase64(int16Array.buffer);

        const audioMessage = {
            type: 'audio_data',
            session_id: this.sessionId,
            sequence_id: ++this.sequenceId,
            audio_data: audioBase64,
            is_final: isFinal,
            timestamp: Date.now()
        };

        this.send(audioMessage);
    }

    stopRecording() {
        this.isRecording = false;

        // 发送最后一个音频块
        if (this.sessionId) {
            this.sendAudioData(new Float32Array(0), true);

            // 发送停止录音消息
            this.send({
                type: 'stop_recording',
                session_id: this.sessionId,
                timestamp: Date.now()
            });
        }

        // 清理音频资源
        if (this.audioProcessor) {
            this.audioProcessor.disconnect();
        }
        if (this.audioContext) {
            this.audioContext.close();
        }
        if (this.audioStream) {
            this.audioStream.getTracks().forEach(track => track.stop());
        }
    }

    disconnect() {
        if (this.sessionId) {
            this.send({
                type: 'disconnect_request',
                session_id: this.sessionId,
                timestamp: Date.now()
            });
        }

        if (this.websocket) {
            this.websocket.close();
        }

        this.stopRecording();
    }

    send(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接');
        }
    }

    generateClientId() {
        return 'client_' + Math.random().toString(36).substr(2, 9);
    }

    arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }
}

// 使用示例
const client = new ASRWebSocketClient('ws://localhost:8080/ws/stream');

client.onConnected = (info) => {
    console.log('连接成功:', info);
    document.getElementById('status').textContent = '已连接';
};

client.onResult = (result) => {
    console.log('识别结果:', result);
    const resultDiv = document.getElementById('result');
    if (result.isFinal) {
        resultDiv.innerHTML += `<p><strong>${result.text}</strong> (${result.confidence.toFixed(2)})</p>`;
    } else {
        resultDiv.innerHTML = `<p style="color: gray;">${result.text}</p>`;
    }
};

client.onError = (error) => {
    console.error('错误:', error);
    document.getElementById('status').textContent = '错误: ' + error.message;
};

// 连接并开始识别
async function startASR() {
    try {
        await client.connect({
            language: 'zh',
            autoLanguageDetection: true,
            enableIntermediateResult: true
        });

        await client.startRecording();
        document.getElementById('startBtn').disabled = true;
        document.getElementById('stopBtn').disabled = false;

    } catch (error) {
        console.error('启动失败:', error);
    }
}

function stopASR() {
    client.stopRecording();
    document.getElementById('startBtn').disabled = false;
    document.getElementById('stopBtn').disabled = true;
}
```

##### Python客户端示例

```python
# websocket_client.py
import asyncio
import websockets
import json
import base64
import numpy as np
from typing import Optional, Callable, Dict, Any

class ASRWebSocketClient:
    """ASR WebSocket客户端"""

    def __init__(self, server_url: str = "ws://localhost:8080/ws/stream"):
        self.server_url = server_url
        self.websocket = None
        self.session_id = None
        self.sequence_id = 0
        self.is_recording = False

        # 事件回调
        self.on_connected: Optional[Callable] = None
        self.on_result: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None

    async def connect(self, config: Dict[str, Any] = None):
        """连接到ASR服务器"""
        if config is None:
            config = {}

        try:
            self.websocket = await websockets.connect(self.server_url)
            print("WebSocket连接已建立")

            # 发送握手请求
            await self.send_handshake(config)

            # 开始消息处理循环
            await self.message_loop()

        except Exception as e:
            print(f"连接失败: {e}")
            if self.on_error:
                self.on_error(e)
            raise

    async def send_handshake(self, config: Dict[str, Any]):
        """发送握手请求"""
        handshake_request = {
            "type": "handshake_request",
            "client_id": config.get("client_id", self.generate_client_id()),
            "language": config.get("language"),
            "auto_language_detection": config.get("auto_language_detection", True),
            "sample_rate": config.get("sample_rate", 16000),
            "channels": config.get("channels", 1),
            "sample_width": config.get("sample_width", 2),
            "chunk_duration": config.get("chunk_duration", 0.4),
            "enable_intermediate_result": config.get("enable_intermediate_result", True),
            "enable_punctuation": config.get("enable_punctuation", True),
            "enable_itn": config.get("enable_itn", True),
            "custom_separator": config.get("custom_separator"),
            "hotwords": config.get("hotwords"),
            "timestamp": asyncio.get_event_loop().time()
        }

        await self.send(handshake_request)

    async def message_loop(self):
        """消息处理循环"""
        try:
            async for message_str in self.websocket:
                message = json.loads(message_str)
                await self.handle_message(message)
        except websockets.exceptions.ConnectionClosed:
            print("WebSocket连接已关闭")
            if self.on_disconnected:
                self.on_disconnected()
        except Exception as e:
            print(f"消息处理错误: {e}")
            if self.on_error:
                self.on_error(e)

    async def handle_message(self, message: Dict[str, Any]):
        """处理收到的消息"""
        message_type = message.get("type")

        if message_type == "handshake_response":
            await self.handle_handshake_response(message)
        elif message_type in ["recognition_result", "intermediate_result"]:
            await self.handle_recognition_result(message)
        elif message_type == "status_update":
            await self.handle_status_update(message)
        elif message_type == "error":
            await self.handle_error(message)
        else:
            print(f"未知消息类型: {message_type}")

    async def handle_handshake_response(self, message: Dict[str, Any]):
        """处理握手响应"""
        if message.get("status") == "success":
            self.session_id = message.get("session_id")
            print(f"握手成功，会话ID: {self.session_id}")

            if self.on_connected:
                self.on_connected({
                    "session_id": self.session_id,
                    "server_config": message.get("server_config")
                })
        else:
            error_msg = message.get("message", "握手失败")
            print(f"握手失败: {error_msg}")
            if self.on_error:
                self.on_error(Exception(error_msg))

    async def handle_recognition_result(self, message: Dict[str, Any]):
        """处理识别结果"""
        if self.on_result:
            self.on_result({
                "text": message.get("text", ""),
                "is_final": message.get("is_final", False),
                "confidence": message.get("confidence", 0.0),
                "language": message.get("language"),
                "processing_time": message.get("processing_time"),
                "timestamp": message.get("timestamp")
            })

    async def handle_status_update(self, message: Dict[str, Any]):
        """处理状态更新"""
        print(f"状态更新: {message.get('state')} - {message.get('message')}")

    async def handle_error(self, message: Dict[str, Any]):
        """处理错误消息"""
        error_msg = f"[{message.get('error_code')}] {message.get('message')}"
        print(f"服务器错误: {error_msg}")
        if self.on_error:
            self.on_error(Exception(error_msg))

    async def start_recording(self):
        """开始录音"""
        if not self.session_id:
            raise Exception("会话未建立")

        self.is_recording = True
        self.sequence_id = 0

        # 发送开始录音消息
        await self.send({
            "type": "start_recording",
            "session_id": self.session_id,
            "timestamp": asyncio.get_event_loop().time()
        })

    async def send_audio_data(self, audio_data: np.ndarray, is_final: bool = False):
        """发送音频数据"""
        if not self.session_id or not self.is_recording:
            return

        # 转换为int16
        if audio_data.dtype != np.int16:
            audio_data = (audio_data * 32767).astype(np.int16)

        # 转换为base64
        audio_bytes = audio_data.tobytes()
        audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')

        audio_message = {
            "type": "audio_data",
            "session_id": self.session_id,
            "sequence_id": self.sequence_id + 1,
            "audio_data": audio_base64,
            "is_final": is_final,
            "timestamp": asyncio.get_event_loop().time()
        }

        self.sequence_id += 1
        await self.send(audio_message)

    async def stop_recording(self):
        """停止录音"""
        self.is_recording = False

        if self.session_id:
            # 发送最后一个空音频块
            await self.send_audio_data(np.array([], dtype=np.int16), True)

            # 发送停止录音消息
            await self.send({
                "type": "stop_recording",
                "session_id": self.session_id,
                "timestamp": asyncio.get_event_loop().time()
            })

    async def disconnect(self):
        """断开连接"""
        if self.session_id:
            await self.send({
                "type": "disconnect_request",
                "session_id": self.session_id,
                "timestamp": asyncio.get_event_loop().time()
            })

        if self.websocket:
            await self.websocket.close()

    async def send(self, message: Dict[str, Any]):
        """发送消息"""
        if self.websocket:
            await self.websocket.send(json.dumps(message))
        else:
            raise Exception("WebSocket未连接")

    def generate_client_id(self) -> str:
        """生成客户端ID"""
        import uuid
        return f"client_{uuid.uuid4().hex[:8]}"

# 使用示例
async def main():
    client = ASRWebSocketClient("ws://localhost:8080/ws/stream")

    # 设置事件回调
    def on_connected(info):
        print(f"连接成功: {info}")

    def on_result(result):
        print(f"识别结果: {result['text']} (置信度: {result['confidence']:.2f})")

    def on_error(error):
        print(f"错误: {error}")

    client.on_connected = on_connected
    client.on_result = on_result
    client.on_error = on_error

    try:
        # 连接到服务器
        await client.connect({
            "language": "zh",
            "auto_language_detection": True,
            "enable_intermediate_result": True
        })

    except Exception as e:
        print(f"连接失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

#### 消息格式

```typescript
// 基础消息结构
interface BaseMessage {
    type: string;
    timestamp: number;
    sequence_id?: number;
}

// 握手请求
interface HandshakeRequest extends BaseMessage {
    type: 'handshake';
    client_id: string;
    language: string;
    auto_language_detection: boolean;
    audio_config: {
        sample_rate: number;
        channels: number;
        chunk_duration: number;
    };
}

// 音频数据
interface AudioDataMessage extends BaseMessage {
    type: 'audio_data';
    audio_data: number[];  // Int16Array
    is_last: boolean;
}

// 识别结果
interface RecognitionResult extends BaseMessage {
    type: 'recognition_result';
    text: string;
    is_final: boolean;
    confidence: number;
    language: string;
    processing_time: number;
}
```

#### WebSocket协议状态转换

```mermaid
stateDiagram-v2
    [*] --> DISCONNECTED: 初始状态

    DISCONNECTED --> CONNECTING: 建立连接
    CONNECTING --> HANDSHAKING: 连接成功
    CONNECTING --> DISCONNECTED: 连接失败

    HANDSHAKING --> CONNECTED: 握手成功
    HANDSHAKING --> DISCONNECTED: 握手失败
    HANDSHAKING --> DISCONNECTED: 握手超时

    CONNECTED --> RECORDING: 开始录音
    CONNECTED --> DISCONNECTING: 主动断开
    CONNECTED --> DISCONNECTED: 连接异常

    RECORDING --> PROCESSING: 处理音频
    RECORDING --> CONNECTED: 停止录音
    RECORDING --> DISCONNECTING: 主动断开
    RECORDING --> DISCONNECTED: 连接异常

    PROCESSING --> RECORDING: 处理完成
    PROCESSING --> DISCONNECTING: 主动断开
    PROCESSING --> DISCONNECTED: 处理错误

    DISCONNECTING --> DISCONNECTED: 断开完成

    DISCONNECTED --> [*]: 清理资源

    note right of HANDSHAKING
        握手阶段交换：
        - 客户端信息
        - 音频配置
        - 语种设置
        - 服务器能力
    end note

    note right of RECORDING
        录音阶段处理：
        - 音频数据接收
        - VAD检测
        - 心跳维持
    end note

    note right of PROCESSING
        处理阶段包含：
        - 语种识别
        - ASR推理
        - 文本后处理
        - 结果返回
    end note
```

### HTTP API

#### RESTful API详细文档

##### 1. 语种管理API

**获取支持的语种列表**
```http
GET /api/languages
```

响应示例：
```json
{
  "success": true,
  "languages": [
    {
      "code": "zh",
      "name": "中文",
      "separator": "，",
      "features": {
        "punctuation": true,
        "itn": true,
        "hotwords": true
      }
    },
    {
      "code": "en",
      "name": "English",
      "separator": ", ",
      "features": {
        "punctuation": true,
        "itn": true,
        "hotwords": false
      }
    }
  ]
}
```

**获取特定语种配置**
```http
GET /api/languages/{language_code}
```

响应示例：
```json
{
  "success": true,
  "language": {
    "code": "zh",
    "name": "中文",
    "separator": "，",
    "silence_threshold": 0.35,
    "model_info": {
      "encoder": "models/zh/encoder.onnx",
      "ctc": "models/zh/ctc.onnx",
      "vocabulary_size": 4233
    },
    "features": {
      "enable_punctuation": true,
      "enable_itn": true,
      "enable_hotwords": true
    }
  }
}
```

##### 2. 服务器状态API

**获取服务器状态**
```http
GET /api/status
```

响应示例：
```json
{
  "success": true,
  "server": {
    "version": "1.0.0",
    "uptime": 3600,
    "status": "running"
  },
  "resources": {
    "cpu_usage": 45.2,
    "memory_usage": 2048,
    "disk_usage": 15.6
  },
  "sessions": {
    "active": 5,
    "total": 127
  },
  "models": {
    "loaded": ["zh", "en", "lid"],
    "status": "ready"
  }
}
```

**健康检查**
```http
GET /api/monitoring/health
```

响应示例：
```json
{
  "status": "healthy",
  "checks": {
    "memory": {
      "status": "healthy",
      "memory_percent": 65.4,
      "available_mb": 1024
    },
    "disk": {
      "status": "healthy",
      "disk_percent": 15.6,
      "available_gb": 85.4
    },
    "models": {
      "status": "healthy",
      "loaded_models": 3,
      "failed_models": 0
    }
  },
  "timestamp": **********.123
}
```

##### 3. 性能监控API

**获取性能统计**
```http
GET /api/monitoring/performance/stats?operation=asr_process&time_window=3600
```

响应示例：
```json
{
  "success": true,
  "time_window": 3600,
  "statistics": {
    "asr_process_chunk": {
      "count": 1250,
      "avg_duration_ms": 85.6,
      "min_duration_ms": 45.2,
      "max_duration_ms": 156.8,
      "p95_duration_ms": 125.4,
      "error_rate": 0.02
    },
    "vad_detect": {
      "count": 2500,
      "avg_duration_ms": 12.3,
      "min_duration_ms": 8.1,
      "max_duration_ms": 25.6,
      "p95_duration_ms": 18.9,
      "error_rate": 0.001
    }
  }
}
```

**获取实时指标**
```http
GET /api/monitoring/metrics/realtime
```

响应示例：
```json
{
  "success": true,
  "timestamp": **********.123,
  "metrics": {
    "requests_per_second": 15.6,
    "avg_response_time_ms": 89.4,
    "active_connections": 8,
    "memory_usage_mb": 2048,
    "cpu_usage_percent": 45.2,
    "error_rate": 0.015
  }
}
```

##### 4. 配置管理API

**获取服务器配置**
```http
GET /api/config
```

响应示例：
```json
{
  "success": true,
  "config": {
    "server": {
      "host": "0.0.0.0",
      "port": 8080,
      "workers": 4
    },
    "audio": {
      "sample_rate": 16000,
      "chunk_duration": 0.4,
      "max_audio_duration": 60
    },
    "asr": {
      "supported_languages": ["zh", "en", "ru"],
      "enable_auto_language_detection": true,
      "enable_intermediate_result": true
    }
  }
}
```

**更新配置（需要管理员权限）**
```http
PUT /api/config
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "audio": {
    "chunk_duration": 0.5
  },
  "asr": {
    "enable_intermediate_result": false
  }
}
```

##### 5. 会话管理API

**获取活跃会话列表**
```http
GET /api/sessions
```

响应示例：
```json
{
  "success": true,
  "sessions": [
    {
      "session_id": "sess_123456",
      "client_id": "client_abc",
      "state": "processing",
      "language": "zh",
      "created_time": 1640995000.123,
      "last_activity": 1640995180.456,
      "audio_duration": 15.6,
      "recognition_count": 8
    }
  ],
  "total": 5,
  "active": 3
}
```

**获取特定会话信息**
```http
GET /api/sessions/{session_id}
```

**强制断开会话（需要管理员权限）**
```http
DELETE /api/sessions/{session_id}
Authorization: Bearer <admin_token>
```

---

## 🔄 数据流程

### 音频处理流程图

```mermaid
flowchart TD
    START([开始]) --> CONNECT[建立WebSocket连接]
    CONNECT --> HANDSHAKE[握手协商]
    HANDSHAKE --> CONFIG{配置验证}
    CONFIG -->|失败| ERROR_HANDSHAKE[握手错误]
    CONFIG -->|成功| SESSION_CREATE[创建会话]

    SESSION_CREATE --> WAIT_AUDIO[等待音频数据]
    WAIT_AUDIO --> RECEIVE_AUDIO[接收音频块]
    RECEIVE_AUDIO --> DECODE_AUDIO[解码音频数据]

    DECODE_AUDIO --> VAD_CHECK{VAD检测}
    VAD_CHECK -->|静音| WAIT_AUDIO
    VAD_CHECK -->|有语音| AUTO_LID{自动语种识别?}

    AUTO_LID -->|是| LID_STATE{LID状态}
    AUTO_LID -->|否| ASR_PROCESS[ASR识别]

    LID_STATE -->|等待语音| PROGRESSIVE_LID[渐进式LID]
    LID_STATE -->|检测中| PROGRESSIVE_LID
    LID_STATE -->|已检测| ASR_PROCESS

    PROGRESSIVE_LID --> LID_RESULT{LID结果}
    LID_RESULT -->|继续检测| WAIT_AUDIO
    LID_RESULT -->|检测完成| INIT_ASR[初始化ASR引擎]
    LID_RESULT -->|检测失败| FALLBACK_LANG[使用默认语种]

    INIT_ASR --> ASR_PROCESS
    FALLBACK_LANG --> ASR_PROCESS

    ASR_PROCESS --> FEATURE_EXTRACT[特征提取]
    FEATURE_EXTRACT --> ENCODER_INFER[编码器推理]
    ENCODER_INFER --> CTC_INFER[CTC推理]
    CTC_INFER --> BEAM_SEARCH[束搜索解码]
    BEAM_SEARCH --> TEXT_PROCESS[文本后处理]

    TEXT_PROCESS --> RESULT_CHECK{有识别结果?}
    RESULT_CHECK -->|是| SEND_RESULT[发送识别结果]
    RESULT_CHECK -->|否| WAIT_AUDIO

    SEND_RESULT --> FINAL_CHECK{最终结果?}
    FINAL_CHECK -->|否| WAIT_AUDIO
    FINAL_CHECK -->|是| SESSION_END[会话结束]

    ERROR_HANDSHAKE --> CLOSE_CONN[关闭连接]
    SESSION_END --> CLEANUP[清理资源]
    CLEANUP --> CLOSE_CONN
    CLOSE_CONN --> END([结束])

    %% 错误处理
    RECEIVE_AUDIO -->|超时| TIMEOUT_ERROR[超时错误]
    DECODE_AUDIO -->|失败| DECODE_ERROR[解码错误]
    FEATURE_EXTRACT -->|失败| PROCESS_ERROR[处理错误]
    ENCODER_INFER -->|失败| INFER_ERROR[推理错误]

    TIMEOUT_ERROR --> ERROR_RESPONSE[发送错误响应]
    DECODE_ERROR --> ERROR_RESPONSE
    PROCESS_ERROR --> ERROR_RESPONSE
    INFER_ERROR --> ERROR_RESPONSE
    ERROR_RESPONSE --> CLOSE_CONN

    %% 样式
    classDef startEnd fill:#4caf50,stroke:#2e7d32,color:#fff
    classDef process fill:#2196f3,stroke:#1565c0,color:#fff
    classDef decision fill:#ff9800,stroke:#ef6c00,color:#fff
    classDef error fill:#f44336,stroke:#c62828,color:#fff

    class START,END startEnd
    class CONNECT,HANDSHAKE,SESSION_CREATE,RECEIVE_AUDIO,DECODE_AUDIO,PROGRESSIVE_LID,INIT_ASR,ASR_PROCESS,FEATURE_EXTRACT,ENCODER_INFER,CTC_INFER,BEAM_SEARCH,TEXT_PROCESS,SEND_RESULT,SESSION_END,CLEANUP,CLOSE_CONN process
    class CONFIG,VAD_CHECK,AUTO_LID,LID_STATE,LID_RESULT,RESULT_CHECK,FINAL_CHECK decision
    class ERROR_HANDSHAKE,TIMEOUT_ERROR,DECODE_ERROR,PROCESS_ERROR,INFER_ERROR,ERROR_RESPONSE error
```

### 会话处理流程

```python
async def process_session_workflow(session: Session, audio_data: bytes):
    """会话处理工作流"""
    
    # 1. VAD检测
    is_speech = session.vad_processor.is_speech_chunk(audio_data)
    if not is_speech:
        return {"type": "silence"}
    
    # 2. 语种检测（如果启用）
    if session.auto_language_detection and not session.language_detected:
        language, confidence = await session.lid_engine.detect_language(audio_data)
        if language:
            session.switch_language(language)
    
    # 3. ASR识别
    result = await session.asr_engine.process_audio_chunk(audio_data)
    
    # 4. 返回结果
    return {
        "type": "recognition_result",
        "text": result.get("text", ""),
        "is_final": result.get("is_final", False),
        "confidence": result.get("confidence", 0.0),
        "language": session.current_language
    }
```

---

## 🚀 扩展开发

### 1. 添加新语种支持

#### 步骤1: 准备模型文件

```bash
# 创建语种目录
mkdir models/new_language

# 放置必需文件
models/new_language/
├── encoder.onnx      # 编码器模型
├── ctc.onnx         # CTC模型
├── units.txt        # 词汇表
└── hotwords.txt     # 热词文件(可选)
```

#### 步骤2: 创建配置文件

```yaml
# configs/lang_configs/new_language.yaml
code: "new_lang"
name: "New Language"
separator: ",.!?;:"
silence_threshold: 0.35

model:
  model_path: "models/new_language"
  dict_path: "models/new_language/units.txt"
  hotwords_path: "models/new_language/hotwords.txt"
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7

features:
  enable_punctuation: true
  enable_itn: true
  enable_hotwords: true
```

#### 步骤3: 更新LID配置

```yaml
# configs/lid_config.yaml
language_mapping:
  0: "zh"
  1: "en"
  2: "ru"
  3: "ug"
  4: "kk"
  5: "new_lang"  # 添加新语种
```

#### 步骤4: 实现语种特定处理

```python
# core/asr/text_processor.py
class TextProcessor:
    def __init__(self, language: str):
        self.language = language
        self.processors = {
            'zh': self._process_chinese,
            'en': self._process_english,
            'new_lang': self._process_new_language  # 添加处理器
        }

    def _process_new_language(self, text: str) -> str:
        """新语种文本后处理"""
        # 实现特定的文本处理逻辑
        return text
```

### 2. 自定义音频处理器

#### 创建自定义VAD

```python
# core/audio/custom_vad.py
from .base_vad import BaseVAD

class CustomVAD(BaseVAD):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # 初始化自定义VAD

    def is_speech_chunk(self, audio_chunk: np.ndarray) -> bool:
        """自定义语音检测逻辑"""
        # 实现检测算法
        return True

    def detect_speech_segments(self, audio: np.ndarray) -> List[Tuple[float, float]]:
        """检测语音段"""
        # 实现分段算法
        return []
```

#### 注册自定义处理器

```python
# core/audio/__init__.py
from .vad_processor import VADProcessor
from .custom_vad import CustomVAD

VAD_REGISTRY = {
    'webrtcvad': VADProcessor,
    'custom': CustomVAD
}

def create_vad_processor(vad_type: str, config: Dict[str, Any]):
    """VAD处理器工厂函数"""
    if vad_type not in VAD_REGISTRY:
        raise ValueError(f"Unknown VAD type: {vad_type}")

    return VAD_REGISTRY[vad_type](config)
```

### 3. 扩展监控指标

#### 添加自定义指标

```python
# utils/monitoring/custom_metrics.py
from .performance_monitor import global_performance_monitor

class CustomMetrics:
    def __init__(self):
        self.custom_counters = {}

    def increment_counter(self, name: str, value: int = 1):
        """增加计数器"""
        self.custom_counters[name] = self.custom_counters.get(name, 0) + value

    def record_custom_metric(self, session_id: str, metric_name: str, value: float):
        """记录自定义指标"""
        global_performance_monitor.record_operation(
            session_id, f"custom_{metric_name}", value
        )

    def get_custom_stats(self) -> Dict[str, Any]:
        """获取自定义统计"""
        return {
            "counters": self.custom_counters,
            "timestamp": time.time()
        }

# 全局实例
custom_metrics = CustomMetrics()
```

#### 集成到监控API

```python
# api/monitoring/monitoring_api.py
@router.get("/custom/metrics")
async def get_custom_metrics() -> Dict[str, Any]:
    """获取自定义指标"""
    from ...utils.monitoring.custom_metrics import custom_metrics
    return custom_metrics.get_custom_stats()
```

### 4. 插件系统设计

#### 插件接口定义

```python
# core/plugins/base_plugin.py
from abc import ABC, abstractmethod
from typing import Dict, Any

class BasePlugin(ABC):
    """插件基类"""

    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        pass

    @abstractmethod
    def process(self, data: Any) -> Any:
        """处理数据"""
        pass

    @abstractmethod
    def cleanup(self):
        """清理资源"""
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass

    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
```

#### 插件管理器

```python
# core/plugins/plugin_manager.py
class PluginManager:
    def __init__(self):
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}

    def register_plugin(self, plugin: BasePlugin, config: Dict[str, Any] = None):
        """注册插件"""
        if plugin.initialize(config or {}):
            self.plugins[plugin.name] = plugin
            self.plugin_configs[plugin.name] = config
            logger.info(f"Plugin registered: {plugin.name} v{plugin.version}")
        else:
            logger.error(f"Failed to initialize plugin: {plugin.name}")

    def get_plugin(self, name: str) -> Optional[BasePlugin]:
        """获取插件"""
        return self.plugins.get(name)

    def process_with_plugin(self, plugin_name: str, data: Any) -> Any:
        """使用插件处理数据"""
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.process(data)
        return data

    def cleanup_all(self):
        """清理所有插件"""
        for plugin in self.plugins.values():
            try:
                plugin.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up plugin {plugin.name}: {e}")

# 全局插件管理器
global_plugin_manager = PluginManager()
```

---

## 🧪 测试指南

### 1. 测试架构

```
tests/
├── unit/                  # 单元测试
│   ├── test_session_manager.py
│   ├── test_asr_engine.py
│   ├── test_lid_engine.py
│   └── test_vad_processor.py
├── integration/           # 集成测试
│   ├── test_websocket_api.py
│   ├── test_http_api.py
│   └── test_end_to_end.py
├── performance/           # 性能测试
│   ├── test_load.py
│   ├── test_stress.py
│   └── test_benchmark.py
├── fixtures/              # 测试数据
│   ├── audio_samples/
│   └── config_samples/
└── conftest.py           # pytest配置
```

### 2. 单元测试示例

```python
# tests/unit/test_session_manager.py
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from core.session import SessionManager
from api.websocket.protocol import HandshakeRequest

class TestSessionManager:

    @pytest.fixture
    async def session_manager(self):
        """创建测试用的会话管理器"""
        config_manager = Mock()
        config_manager.get_supported_languages.return_value = ["zh", "en"]

        manager = SessionManager(config_manager)
        yield manager
        await manager.shutdown()

    @pytest.mark.asyncio
    async def test_create_session_success(self, session_manager):
        """测试成功创建会话"""
        handshake_req = HandshakeRequest(
            client_id="test_client",
            language="zh",
            auto_language_detection=False
        )

        websocket = Mock()
        success = await session_manager.create_session(
            "test_session", "test_client", handshake_req, websocket
        )

        assert success
        assert "test_session" in session_manager.sessions

    @pytest.mark.asyncio
    async def test_process_audio_data(self, session_manager):
        """测试音频数据处理"""
        # 先创建会话
        await self._create_test_session(session_manager)

        # 模拟音频数据
        audio_message = Mock()
        audio_message.audio_data = [0] * 1600  # 0.1秒的音频
        audio_message.is_last = False

        result = await session_manager.process_audio_data("test_session", audio_message)

        assert result["success"] is True
        assert "text" in result

    async def _create_test_session(self, session_manager):
        """创建测试会话的辅助方法"""
        handshake_req = HandshakeRequest(
            client_id="test_client",
            language="zh",
            auto_language_detection=False
        )
        websocket = Mock()
        await session_manager.create_session("test_session", "test_client", handshake_req, websocket)
```

### 3. 集成测试示例

```python
# tests/integration/test_websocket_api.py
import pytest
import json
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocket

from server import app

class TestWebSocketAPI:

    @pytest.fixture
    def client(self):
        return TestClient(app)

    def test_websocket_handshake(self, client):
        """测试WebSocket握手"""
        with client.websocket_connect("/ws/stream") as websocket:
            # 发送握手消息
            handshake = {
                "type": "handshake",
                "client_id": "test_client",
                "language": "zh",
                "auto_language_detection": False,
                "audio_config": {
                    "sample_rate": 16000,
                    "channels": 1,
                    "chunk_duration": 0.4
                }
            }
            websocket.send_json(handshake)

            # 接收响应
            response = websocket.receive_json()

            assert response["type"] == "handshake_response"
            assert response["success"] is True

    def test_audio_processing_workflow(self, client):
        """测试完整的音频处理流程"""
        with client.websocket_connect("/ws/stream") as websocket:
            # 握手
            self._perform_handshake(websocket)

            # 发送音频数据
            audio_data = [0] * 6400  # 0.4秒的静音
            audio_message = {
                "type": "audio_data",
                "sequence_id": 1,
                "audio_data": audio_data,
                "is_last": False,
                "timestamp": **********000
            }
            websocket.send_json(audio_message)

            # 接收处理结果
            response = websocket.receive_json()

            assert response["type"] in ["recognition_result", "silence"]

    def _perform_handshake(self, websocket):
        """执行握手的辅助方法"""
        handshake = {
            "type": "handshake",
            "client_id": "test_client",
            "language": "zh",
            "auto_language_detection": False,
            "audio_config": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_duration": 0.4
            }
        }
        websocket.send_json(handshake)
        response = websocket.receive_json()
        assert response["success"] is True
```

### 4. 性能测试

```python
# tests/performance/test_load.py
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

class LoadTest:

    def __init__(self, base_url: str = "ws://localhost:8080"):
        self.base_url = base_url
        self.results = []

    async def single_session_test(self, session_id: str) -> Dict[str, float]:
        """单个会话的性能测试"""
        start_time = time.time()

        try:
            # 建立连接和握手
            connect_start = time.time()
            # ... WebSocket连接逻辑
            connect_time = time.time() - connect_start

            # 发送音频数据
            process_start = time.time()
            # ... 音频处理逻辑
            process_time = time.time() - process_start

            total_time = time.time() - start_time

            return {
                "session_id": session_id,
                "connect_time": connect_time,
                "process_time": process_time,
                "total_time": total_time,
                "success": True
            }

        except Exception as e:
            return {
                "session_id": session_id,
                "error": str(e),
                "success": False
            }

    async def run_load_test(self, concurrent_sessions: int = 10, duration: int = 60):
        """运行负载测试"""
        print(f"Starting load test: {concurrent_sessions} concurrent sessions for {duration}s")

        tasks = []
        for i in range(concurrent_sessions):
            task = asyncio.create_task(
                self.single_session_test(f"session_{i}")
            )
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 分析结果
        self.analyze_results(results)

    def analyze_results(self, results: List[Dict[str, Any]]):
        """分析测试结果"""
        successful_results = [r for r in results if r.get("success", False)]
        failed_count = len(results) - len(successful_results)

        if successful_results:
            connect_times = [r["connect_time"] for r in successful_results]
            process_times = [r["process_time"] for r in successful_results]
            total_times = [r["total_time"] for r in successful_results]

            print(f"Success Rate: {len(successful_results)}/{len(results)} ({len(successful_results)/len(results)*100:.1f}%)")
            print(f"Failed Sessions: {failed_count}")
            print(f"Average Connect Time: {statistics.mean(connect_times):.3f}s")
            print(f"Average Process Time: {statistics.mean(process_times):.3f}s")
            print(f"Average Total Time: {statistics.mean(total_times):.3f}s")
            print(f"P95 Total Time: {statistics.quantiles(total_times, n=20)[18]:.3f}s")
        else:
            print("All sessions failed!")

if __name__ == "__main__":
    load_test = LoadTest()
    asyncio.run(load_test.run_load_test(concurrent_sessions=50, duration=120))
```

### 5. 测试配置

```python
# conftest.py
import pytest
import asyncio
from pathlib import Path

# 测试配置
TEST_CONFIG = {
    "server": {
        "host": "127.0.0.1",
        "port": 8081,  # 使用不同端口避免冲突
        "log_level": "DEBUG"
    },
    "audio": {
        "sample_rate": 16000,
        "channels": 1,
        "chunk_duration": 0.4
    }
}

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def test_audio_data():
    """测试音频数据"""
    # 生成0.4秒的测试音频（16kHz, 16bit, mono）
    sample_rate = 16000
    duration = 0.4
    samples = int(sample_rate * duration)

    # 生成正弦波测试信号
    import numpy as np
    t = np.linspace(0, duration, samples)
    frequency = 440  # A4音符
    audio = np.sin(2 * np.pi * frequency * t) * 0.5

    # 转换为int16
    audio_int16 = (audio * 32767).astype(np.int16)
    return audio_int16.tolist()

@pytest.fixture
def test_config_dir(tmp_path):
    """创建测试配置目录"""
    config_dir = tmp_path / "test_configs"
    config_dir.mkdir()

    # 创建测试配置文件
    import yaml

    server_config = config_dir / "server_config.yaml"
    with open(server_config, 'w') as f:
        yaml.dump(TEST_CONFIG, f)

    return config_dir
```

---

## ⚡ 性能优化

### 1. 系统级优化

#### 硬件配置建议

```yaml
# 生产环境推荐配置
hardware:
  cpu:
    cores: 8+              # 8核以上CPU
    architecture: x86_64   # 支持AVX2指令集
    frequency: 2.4GHz+     # 主频2.4GHz以上

  memory:
    ram: 16GB+             # 16GB以上内存
    swap: 8GB              # 8GB交换空间

  storage:
    type: SSD              # SSD存储
    space: 100GB+          # 100GB以上空间
    iops: 3000+            # 3000 IOPS以上

  network:
    bandwidth: 1Gbps+      # 千兆网络
    latency: <10ms         # 低延迟网络
```

#### 操作系统优化

```bash
# 1. 内核参数优化
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf
sysctl -p

# 2. 文件描述符限制
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf

# 3. CPU调度优化
echo 'performance' > /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

### 2. 应用级优化

#### ONNX Runtime优化

```python
# config/optimization.yaml
onnx_optimization:
  # 执行提供者优化
  providers:
    - name: "CPUExecutionProvider"
      options:
        enable_cpu_mem_arena: true
        arena_extend_strategy: "kSameAsRequested"
        cpu_allocator: "arena"
        intra_op_num_threads: 4
        inter_op_num_threads: 2

  # 会话配置优化
  session_options:
    enable_mem_pattern: true
    enable_mem_reuse: true
    enable_cpu_mem_arena: true
    execution_mode: "sequential"  # 或 "parallel"
    graph_optimization_level: "all"

  # 模型优化
  model_optimization:
    quantization: true
    optimization_level: "all"
    enable_gelu_approximation: true
```

#### 内存管理优化

```python
# core/optimization/memory_manager.py
class MemoryManager:
    """内存管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.max_memory_usage = config.get('max_memory_mb', 4096)
        self.gc_threshold = config.get('gc_threshold', 0.8)
        self.enable_memory_pool = config.get('enable_memory_pool', True)

    def optimize_memory_usage(self):
        """优化内存使用"""
        import gc
        import psutil

        # 获取当前内存使用
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024

        # 如果内存使用超过阈值，触发垃圾回收
        if memory_mb > self.max_memory_usage * self.gc_threshold:
            gc.collect()

        # 清理ONNX会话缓存
        self._cleanup_onnx_cache()

    def _cleanup_onnx_cache(self):
        """清理ONNX缓存"""
        # 实现ONNX会话池的缓存清理
        pass
```

#### 并发处理优化

```python
# core/optimization/concurrency.py
import asyncio
from concurrent.futures import ThreadPoolExecutor

class ConcurrencyOptimizer:
    """并发优化器"""

    def __init__(self, config: Dict[str, Any]):
        self.max_workers = config.get('max_workers', 4)
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)

    async def optimize_audio_processing(self, audio_chunks: List[np.ndarray]):
        """并行处理音频块"""
        tasks = []

        # 创建并发任务
        for chunk in audio_chunks:
            task = asyncio.create_task(self._process_chunk_async(chunk))
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        return [r for r in results if not isinstance(r, Exception)]

    async def _process_chunk_async(self, chunk: np.ndarray):
        """异步处理单个音频块"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.thread_pool,
            self._process_chunk_sync,
            chunk
        )
```

### 3. 模型优化

#### 模型量化

```python
# scripts/quantize_models.py
import onnx
from onnxruntime.quantization import quantize_dynamic, QuantType

def quantize_model(model_path: str, output_path: str):
    """动态量化ONNX模型"""
    quantize_dynamic(
        model_input=model_path,
        model_output=output_path,
        weight_type=QuantType.QInt8,
        optimize_model=True,
        extra_options={
            'EnableSubgraph': True,
            'ForceQuantizeNoInput': True
        }
    )

    print(f"Model quantized: {model_path} -> {output_path}")

# 使用示例
if __name__ == "__main__":
    models = [
        ("models/zh/encoder.onnx", "models/zh/encoder.quant.onnx"),
        ("models/zh/ctc.onnx", "models/zh/ctc.quant.onnx"),
        ("models/en/encoder.onnx", "models/en/encoder.quant.onnx"),
    ]

    for input_path, output_path in models:
        quantize_model(input_path, output_path)
```

#### 模型剪枝

```python
# scripts/prune_models.py
def prune_model_graph(model_path: str, output_path: str):
    """剪枝ONNX模型图"""
    import onnx
    from onnx import optimizer

    # 加载模型
    model = onnx.load(model_path)

    # 应用优化passes
    optimized_model = optimizer.optimize(model, [
        'eliminate_deadend',
        'eliminate_duplicate_initializer',
        'eliminate_identity',
        'eliminate_nop_dropout',
        'eliminate_nop_monotone_argmax',
        'eliminate_nop_pad',
        'eliminate_nop_transpose',
        'eliminate_unused_initializer',
        'extract_constant_to_initializer',
        'fuse_add_bias_into_conv',
        'fuse_bn_into_conv',
        'fuse_consecutive_concats',
        'fuse_consecutive_log_softmax',
        'fuse_consecutive_reduce_unsqueeze',
        'fuse_consecutive_squeezes',
        'fuse_consecutive_transposes',
        'fuse_matmul_add_bias_into_gemm',
        'fuse_pad_into_conv',
        'fuse_transpose_into_gemm',
        'lift_lexical_references',
    ])

    # 保存优化后的模型
    onnx.save(optimized_model, output_path)
    print(f"Model pruned: {model_path} -> {output_path}")
```

### 4. 缓存策略

#### 多级缓存架构

```python
# core/optimization/cache_manager.py
import time
from typing import Any, Optional
from collections import OrderedDict

class MultiLevelCache:
    """多级缓存管理器"""

    def __init__(self, config: Dict[str, Any]):
        # L1缓存：内存缓存（最快）
        self.l1_cache = OrderedDict()
        self.l1_max_size = config.get('l1_max_size', 100)
        self.l1_ttl = config.get('l1_ttl', 300)  # 5分钟

        # L2缓存：Redis缓存（中等速度）
        self.l2_enabled = config.get('l2_enabled', False)
        self.l2_ttl = config.get('l2_ttl', 3600)  # 1小时

        # L3缓存：磁盘缓存（最慢但容量大）
        self.l3_enabled = config.get('l3_enabled', False)
        self.l3_path = config.get('l3_path', '/tmp/asr_cache')

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        # 尝试L1缓存
        if key in self.l1_cache:
            value, timestamp = self.l1_cache[key]
            if time.time() - timestamp < self.l1_ttl:
                # 移到最前面（LRU）
                self.l1_cache.move_to_end(key)
                return value
            else:
                del self.l1_cache[key]

        # 尝试L2缓存
        if self.l2_enabled:
            value = self._get_from_l2(key)
            if value is not None:
                self._put_to_l1(key, value)
                return value

        # 尝试L3缓存
        if self.l3_enabled:
            value = self._get_from_l3(key)
            if value is not None:
                self._put_to_l1(key, value)
                if self.l2_enabled:
                    self._put_to_l2(key, value)
                return value

        return None

    def put(self, key: str, value: Any):
        """存储缓存值"""
        self._put_to_l1(key, value)

        if self.l2_enabled:
            self._put_to_l2(key, value)

        if self.l3_enabled:
            self._put_to_l3(key, value)

    def _put_to_l1(self, key: str, value: Any):
        """存储到L1缓存"""
        if len(self.l1_cache) >= self.l1_max_size:
            # 删除最旧的项
            self.l1_cache.popitem(last=False)

        self.l1_cache[key] = (value, time.time())
```

### 5. 网络优化

#### WebSocket连接优化

```python
# api/websocket/optimized_handler.py
class OptimizedWebSocketHandler:
    """优化的WebSocket处理器"""

    def __init__(self, config: Dict[str, Any]):
        self.compression_enabled = config.get('compression', True)
        self.batch_size = config.get('batch_size', 10)
        self.batch_timeout = config.get('batch_timeout', 0.1)

    async def handle_audio_batch(self, audio_messages: List[AudioDataMessage]):
        """批量处理音频消息"""
        if not audio_messages:
            return

        # 批量提取音频数据
        audio_chunks = []
        for msg in audio_messages:
            audio_data = self._decode_audio(msg.audio_data)
            audio_chunks.append(audio_data)

        # 并行处理
        results = await self._process_audio_batch(audio_chunks)

        # 批量发送结果
        await self._send_batch_results(results)

    def _compress_message(self, message: str) -> bytes:
        """压缩消息"""
        if self.compression_enabled:
            import gzip
            return gzip.compress(message.encode('utf-8'))
        return message.encode('utf-8')
```

### 6. 监控和调优

#### 性能监控

```python
# utils/monitoring/performance_profiler.py
import time
import psutil
import threading
from collections import defaultdict

class PerformanceProfiler:
    """性能分析器"""

    def __init__(self):
        self.metrics = defaultdict(list)
        self.start_times = {}
        self.lock = threading.Lock()

    def start_timer(self, operation: str):
        """开始计时"""
        with self.lock:
            self.start_times[operation] = time.time()

    def end_timer(self, operation: str):
        """结束计时"""
        with self.lock:
            if operation in self.start_times:
                duration = time.time() - self.start_times[operation]
                self.metrics[f"{operation}_duration"].append(duration)
                del self.start_times[operation]

    def record_memory_usage(self):
        """记录内存使用"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.metrics['memory_usage_mb'].append(memory_mb)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {}

        for metric, values in self.metrics.items():
            if values:
                stats[metric] = {
                    'count': len(values),
                    'avg': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'p95': sorted(values)[int(len(values) * 0.95)] if len(values) > 20 else max(values)
                }

        return stats

# 全局性能分析器
global_profiler = PerformanceProfiler()
```

#### 自动调优

```python
# core/optimization/auto_tuner.py
class AutoTuner:
    """自动调优器"""

    def __init__(self, config: Dict[str, Any]):
        self.target_latency = config.get('target_latency_ms', 100)
        self.target_throughput = config.get('target_throughput_rps', 100)
        self.adjustment_interval = config.get('adjustment_interval', 60)

    async def auto_tune(self):
        """自动调优"""
        while True:
            await asyncio.sleep(self.adjustment_interval)

            # 获取当前性能指标
            stats = global_profiler.get_statistics()

            # 分析性能瓶颈
            bottlenecks = self._analyze_bottlenecks(stats)

            # 应用优化策略
            for bottleneck in bottlenecks:
                await self._apply_optimization(bottleneck)

    def _analyze_bottlenecks(self, stats: Dict[str, Any]) -> List[str]:
        """分析性能瓶颈"""
        bottlenecks = []

        # 检查延迟
        if 'asr_process_chunk_duration' in stats:
            avg_latency = stats['asr_process_chunk_duration']['avg'] * 1000
            if avg_latency > self.target_latency:
                bottlenecks.append('high_latency')

        # 检查内存使用
        if 'memory_usage_mb' in stats:
            max_memory = stats['memory_usage_mb']['max']
            if max_memory > 4096:  # 4GB
                bottlenecks.append('high_memory')

        return bottlenecks

    async def _apply_optimization(self, bottleneck: str):
        """应用优化策略"""
        if bottleneck == 'high_latency':
            # 增加并发处理
            await self._increase_concurrency()
        elif bottleneck == 'high_memory':
            # 触发内存清理
            await self._cleanup_memory()
```

---

## 🔧 故障排查

### 1. 常见问题诊断

#### 启动问题

**问题1: 模块导入错误**
```bash
ModuleNotFoundError: No module named 'onnxruntime'
```

**解决方案:**
```bash
# 检查虚拟环境
python -m venv --version

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import onnxruntime; print(onnxruntime.__version__)"
```

**问题2: 配置文件加载失败**
```bash
FileNotFoundError: [Errno 2] No such file or directory: 'configs/server_config.yaml'
```

**解决方案:**
```bash
# 检查配置文件
ls -la configs/
find . -name "*.yaml" -type f

# 创建缺失的配置文件
cp configs/server_config.yaml.example configs/server_config.yaml

# 验证配置文件格式
python -c "import yaml; yaml.safe_load(open('configs/server_config.yaml'))"
```

**问题3: 端口占用**
```bash
OSError: [Errno 98] Address already in use
```

**解决方案:**
```bash
# 查找占用端口的进程
netstat -tulpn | grep :8080
lsof -i :8080

# 终止占用进程
kill -9 <PID>

# 或修改配置使用其他端口
sed -i 's/port: 8080/port: 8081/' configs/server_config.yaml
```

#### 模型加载问题

**问题1: 模型文件不存在**
```bash
FileNotFoundError: models/zh/encoder.onnx not found
```

**解决方案:**
```bash
# 检查模型目录结构
tree models/

# 下载模型文件（示例）
mkdir -p models/zh models/en models/lid
# 将模型文件放置到对应目录

# 验证模型文件
python -c "
import onnx
model = onnx.load('models/zh/encoder.onnx')
print('Model loaded successfully')
"
```

**问题2: ONNX Runtime版本不兼容**
```bash
RuntimeError: ONNX Runtime version mismatch
```

**解决方案:**
```bash
# 检查ONNX Runtime版本
python -c "import onnxruntime; print(onnxruntime.__version__)"

# 检查模型ONNX版本
python -c "
import onnx
model = onnx.load('models/zh/encoder.onnx')
print(f'ONNX version: {model.opset_import[0].version}')
"

# 升级ONNX Runtime
pip install --upgrade onnxruntime

# 或降级到兼容版本
pip install onnxruntime==1.15.1
```

#### 音频处理问题

**问题1: WebRTC VAD初始化失败**
```bash
ImportError: No module named 'webrtcvad'
```

**解决方案:**
```bash
# 安装WebRTC VAD
pip install webrtcvad

# 如果编译失败，安装系统依赖
# Ubuntu/Debian
sudo apt-get install build-essential python3-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel

# 重新安装
pip uninstall webrtcvad
pip install webrtcvad
```

**问题2: 音频格式不支持**
```bash
ValueError: Unsupported audio format
```

**解决方案:**
```python
# 检查音频参数
def validate_audio_config(sample_rate, channels, sample_width):
    """验证音频配置"""
    supported_rates = [8000, 16000, 32000, 48000]
    if sample_rate not in supported_rates:
        raise ValueError(f"Unsupported sample rate: {sample_rate}")

    if channels not in [1, 2]:
        raise ValueError(f"Unsupported channel count: {channels}")

    if sample_width not in [1, 2]:
        raise ValueError(f"Unsupported sample width: {sample_width}")

# 音频格式转换
import numpy as np

def convert_audio_format(audio_data, target_rate=16000):
    """转换音频格式"""
    # 实现重采样逻辑
    pass
```

### 2. 性能问题诊断

#### 延迟过高

**诊断步骤:**
```python
# 1. 启用性能监控
import time
from utils.monitoring import global_performance_monitor

def diagnose_latency():
    """诊断延迟问题"""
    # 检查各个组件的处理时间
    stats = global_performance_monitor.get_statistics()

    print("=== 延迟分析 ===")
    for operation, metrics in stats.items():
        if 'duration' in operation:
            print(f"{operation}:")
            print(f"  平均: {metrics['avg']*1000:.2f}ms")
            print(f"  P95: {metrics['p95']*1000:.2f}ms")
            print(f"  最大: {metrics['max']*1000:.2f}ms")

# 2. 分析瓶颈
def analyze_bottlenecks():
    """分析性能瓶颈"""
    bottlenecks = []

    # 检查VAD处理时间
    vad_time = get_avg_processing_time('vad_process')
    if vad_time > 0.05:  # 50ms
        bottlenecks.append(f"VAD处理过慢: {vad_time*1000:.2f}ms")

    # 检查ASR处理时间
    asr_time = get_avg_processing_time('asr_process_chunk')
    if asr_time > 0.1:  # 100ms
        bottlenecks.append(f"ASR处理过慢: {asr_time*1000:.2f}ms")

    return bottlenecks
```

**优化方案:**
```python
# 1. 启用模型量化
config = {
    'quantized': True,
    'optimization_level': 'all'
}

# 2. 调整批处理大小
config = {
    'batch_size': 4,  # 减小批处理大小
    'max_batch_delay': 0.05  # 50ms最大延迟
}

# 3. 使用GPU加速
config = {
    'device': 'cuda',
    'providers': ['CUDAExecutionProvider', 'CPUExecutionProvider']
}
```

#### 内存泄漏

**诊断工具:**
```python
import psutil
import gc
import tracemalloc

def diagnose_memory_leak():
    """诊断内存泄漏"""
    # 启用内存跟踪
    tracemalloc.start()

    # 运行一段时间后检查
    process = psutil.Process()
    memory_info = process.memory_info()

    print(f"RSS内存: {memory_info.rss / 1024 / 1024:.2f} MB")
    print(f"VMS内存: {memory_info.vms / 1024 / 1024:.2f} MB")

    # 获取内存快照
    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics('lineno')

    print("=== 内存使用TOP 10 ===")
    for stat in top_stats[:10]:
        print(stat)

def fix_memory_leak():
    """修复内存泄漏"""
    # 1. 强制垃圾回收
    gc.collect()

    # 2. 清理ONNX会话缓存
    # 实现会话池清理逻辑

    # 3. 清理音频缓存
    # 实现音频缓存清理逻辑
```

### 3. 网络问题诊断

#### WebSocket连接问题

**问题1: 连接频繁断开**
```python
def diagnose_websocket_issues():
    """诊断WebSocket问题"""
    # 检查连接统计
    stats = {
        'total_connections': 0,
        'active_connections': 0,
        'failed_connections': 0,
        'avg_connection_duration': 0
    }

    # 分析断开原因
    disconnect_reasons = {
        'timeout': 0,
        'client_error': 0,
        'server_error': 0,
        'network_error': 0
    }

    return stats, disconnect_reasons
```

**解决方案:**
```yaml
# 调整WebSocket配置
websocket:
  heartbeat_interval: 30      # 心跳间隔
  connection_timeout: 300     # 连接超时
  max_message_size: 1048576   # 最大消息大小
  ping_interval: 20           # Ping间隔
  ping_timeout: 10            # Ping超时
```

#### 网络延迟问题

**诊断工具:**
```bash
# 1. 网络延迟测试
ping -c 10 server_ip

# 2. 带宽测试
iperf3 -c server_ip -t 30

# 3. WebSocket延迟测试
wscat -c ws://server_ip:8080/ws/stream
```

### 4. 日志分析

#### 日志配置

```yaml
# configs/logging.yaml
logging:
  version: 1
  disable_existing_loggers: false

  formatters:
    detailed:
      format: '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    simple:
      format: '%(levelname)s - %(message)s'

  handlers:
    console:
      class: logging.StreamHandler
      level: INFO
      formatter: simple
      stream: ext://sys.stdout

    file:
      class: logging.handlers.RotatingFileHandler
      level: DEBUG
      formatter: detailed
      filename: logs/asr_server.log
      maxBytes: 10485760  # 10MB
      backupCount: 5

    error_file:
      class: logging.handlers.RotatingFileHandler
      level: ERROR
      formatter: detailed
      filename: logs/asr_errors.log
      maxBytes: 10485760
      backupCount: 5

  loggers:
    enhanced_stream_asr:
      level: DEBUG
      handlers: [console, file, error_file]
      propagate: false

  root:
    level: INFO
    handlers: [console]
```

#### 日志分析脚本

```python
# scripts/analyze_logs.py
import re
from collections import defaultdict, Counter
from datetime import datetime

def analyze_logs(log_file: str):
    """分析日志文件"""
    error_patterns = {
        'connection_errors': r'WebSocket.*disconnect',
        'model_errors': r'Model.*failed',
        'audio_errors': r'Audio.*error',
        'timeout_errors': r'timeout',
        'memory_errors': r'Memory.*error|OutOfMemory'
    }

    error_counts = defaultdict(int)
    error_timeline = defaultdict(list)

    with open(log_file, 'r') as f:
        for line in f:
            # 提取时间戳
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if timestamp_match:
                timestamp = timestamp_match.group(1)

            # 匹配错误模式
            for error_type, pattern in error_patterns.items():
                if re.search(pattern, line, re.IGNORECASE):
                    error_counts[error_type] += 1
                    error_timeline[error_type].append(timestamp)

    return error_counts, error_timeline

def generate_report(error_counts, error_timeline):
    """生成分析报告"""
    print("=== 错误统计报告 ===")
    for error_type, count in error_counts.items():
        print(f"{error_type}: {count} 次")

    print("\n=== 错误时间分布 ===")
    for error_type, timestamps in error_timeline.items():
        if timestamps:
            print(f"{error_type}: 最近发生时间 {timestamps[-1]}")

if __name__ == "__main__":
    error_counts, error_timeline = analyze_logs("logs/asr_server.log")
    generate_report(error_counts, error_timeline)
```

### 5. 监控和告警

#### 健康检查

```python
# api/monitoring/health_check.py
from typing import Dict, Any
import psutil
import asyncio

class HealthChecker:
    """健康检查器"""

    async def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            'status': 'healthy',
            'checks': {},
            'timestamp': time.time()
        }

        # 检查内存使用
        memory_check = await self._check_memory()
        health_status['checks']['memory'] = memory_check

        # 检查磁盘空间
        disk_check = await self._check_disk_space()
        health_status['checks']['disk'] = disk_check

        # 检查模型状态
        model_check = await self._check_models()
        health_status['checks']['models'] = model_check

        # 检查数据库连接
        db_check = await self._check_database()
        health_status['checks']['database'] = db_check

        # 综合评估
        if any(check['status'] == 'unhealthy' for check in health_status['checks'].values()):
            health_status['status'] = 'unhealthy'
        elif any(check['status'] == 'warning' for check in health_status['checks'].values()):
            health_status['status'] = 'warning'

        return health_status

    async def _check_memory(self) -> Dict[str, Any]:
        """检查内存使用"""
        memory = psutil.virtual_memory()
        memory_percent = memory.percent

        if memory_percent > 90:
            status = 'unhealthy'
        elif memory_percent > 80:
            status = 'warning'
        else:
            status = 'healthy'

        return {
            'status': status,
            'memory_percent': memory_percent,
            'available_mb': memory.available / 1024 / 1024
        }
```

---

## 🚀 部署指南

### 1. 开发环境部署

```bash
# 启动开发服务器
python server.py --debug --reload

# 使用uvicorn直接启动
uvicorn server:app --host 0.0.0.0 --port 8080 --reload
```

### 2. 生产环境部署

#### 使用Gunicorn + Uvicorn

```bash
# 安装生产依赖
pip install gunicorn

# 启动生产服务器
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8080 \
  --timeout 300 \
  --keepalive 2 \
  --max-requests 1000 \
  --max-requests-jitter 100 \
  --preload \
  server:app
```

#### Systemd服务配置

```ini
# /etc/systemd/system/enhanced-stream-asr.service
[Unit]
Description=Enhanced Stream ASR Service
After=network.target

[Service]
Type=exec
User=asr
Group=asr
WorkingDirectory=/opt/enhanced-stream-asr
Environment=PATH=/opt/enhanced-stream-asr/venv/bin
ExecStart=/opt/enhanced-stream-asr/venv/bin/gunicorn \
  -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8080 \
  --timeout 300 \
  server:app
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

#### Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libffi-dev \
    libssl-dev \
    libasound2-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 asr && chown -R asr:asr /app
USER asr

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8080", "server:app"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  asr-server:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./models:/app/models:ro
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    environment:
      - LOG_LEVEL=INFO
      - WORKERS=4
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/monitoring/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - asr-server
    restart: unless-stopped
```

### 3. Kubernetes部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: enhanced-stream-asr
  labels:
    app: enhanced-stream-asr
spec:
  replicas: 3
  selector:
    matchLabels:
      app: enhanced-stream-asr
  template:
    metadata:
      labels:
        app: enhanced-stream-asr
    spec:
      containers:
      - name: asr-server
        image: enhanced-stream-asr:latest
        ports:
        - containerPort: 8080
        env:
        - name: WORKERS
          value: "2"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: models
          mountPath: /app/models
          readOnly: true
        - name: configs
          mountPath: /app/configs
          readOnly: true
        livenessProbe:
          httpGet:
            path: /api/monitoring/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/monitoring/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: models
        persistentVolumeClaim:
          claimName: asr-models-pvc
      - name: configs
        configMap:
          name: asr-configs

---
apiVersion: v1
kind: Service
metadata:
  name: enhanced-stream-asr-service
spec:
  selector:
    app: enhanced-stream-asr
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

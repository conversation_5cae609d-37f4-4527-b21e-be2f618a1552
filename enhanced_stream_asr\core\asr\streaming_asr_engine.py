"""
流式ASR引擎
基于Individual版本的实现，集成编码器、CTC解码器和特征提取
"""

import os
import math
import logging
import time
import numpy as np
import torch
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any, Union

from .symbol_table import SymbolTable
from .feature_pipeline import FeaturePipeline
from ..engines import PooledONNXEngine
from ...utils.exceptions import AudioProcessingError, ErrorCodes, ErrorHandler
from ...utils.monitoring.performance_monitor import PerformanceContext, global_performance_monitor

logger = logging.getLogger(__name__)


class StreamingASREngine:
    """
    流式ASR引擎，支持实时语音识别
    基于Individual版本的架构，使用ONNX模型进行推理
    """
    
    def __init__(self, model_path: str, config: Dict[str, Any]):
        """
        初始化流式ASR引擎
        
        Args:
            model_path: 模型文件路径
            config: 配置参数
        """
        self.model_path = model_path
        self.config = config
        self.language = config.get('language', 'zh')
        
        # 模型组件
        self.encoder_engine = None
        self.ctc_engine = None
        self.decoder_engine = None
        
        # 特征提取和符号表
        self.feature_pipeline = None
        self.symbol_table = None
        
        # 解码参数
        self.chunk_size = config.get('chunk_size', 16)
        self.left_chunks = config.get('left_chunks', 16)
        self.right_context = config.get('right_context', 7)
        self.decoding_window = config.get('decoding_window', 67)
        self.subsampling_rate = config.get('subsampling_rate', 4)
        
        # 添加最大缓存大小配置
        self.max_encoder_outputs = config.get('max_encoder_outputs', 100)

        # 状态变量
        self.is_initialized = False
        self.reset()
        
    def reset(self):
        """重置引擎状态"""
        self.chunk_index = 0
        self.offset = 0
        self.encoder_outputs = []
        self.ctc_tokens = []
        self.ctc_times = []
        self.ctc_offset = 0
        self.result_text = ""
        
        # 编码器缓存
        self.required_cache_size = self.chunk_size * self.left_chunks
        self.att_cache = None
        self.cnn_cache = None
        self.att_mask = None
        
        # CTC搜索缓存
        self.ctc_beam_search_cache = []
        self.last_chunk_tokens = []
        self.last_chunk_times = []
        
    async def initialize(self) -> bool:
        """初始化ASR引擎"""
        try:
            logger.info(f"Initializing StreamingASREngine: {self.model_path}")
            
            # 初始化特征提取管道
            feature_config = self.config.get('feature', {})
            feature_config.setdefault('feat_type', 'fbank')
            feature_config.setdefault('num_mel_bins', 80)
            feature_config.setdefault('sample_rate', 16000)
            self.feature_pipeline = FeaturePipeline(feature_config)
            
            # 初始化符号表
            dict_path = self.config.get('dict_path', '')
            if dict_path and os.path.exists(dict_path):
                self.symbol_table = SymbolTable(dict_path, self.language)
            else:
                logger.warning(f"Dictionary not found: {dict_path}")
                return False
                
            # 初始化ONNX引擎
            await self._init_onnx_engines()
            
            # 初始化编码器缓存
            self._init_encoder_cache()
            
            self.is_initialized = True
            logger.info("StreamingASREngine initialized successfully")
            return True
            
        except Exception as e:
            error_response = ErrorHandler.handle_and_log_exception(
                logger, e, ErrorCodes.MODEL_LOADING_FAILED,
                context="StreamingASREngine initialization"
            )
            return False
            
    async def _init_onnx_engines(self):
        """初始化ONNX引擎"""
        try:
            engine_config = {
                'device': self.config.get('device', 'cpu'),
                'device_id': self.config.get('device_id', 0),
                'min_sessions': self.config.get('min_sessions', 1),
                'max_sessions': self.config.get('max_sessions', 4),
                'session_timeout': self.config.get('session_timeout', 300)
            }
            
            # 编码器
            encoder_path = os.path.join(self.model_path, "encoder.onnx")
            if self.config.get('quantized', False):
                quant_path = os.path.join(self.model_path, "encoder.quant.onnx")
                if os.path.exists(quant_path):
                    encoder_path = quant_path
                    
            self.encoder_engine = PooledONNXEngine(encoder_path, engine_config)
            if not await self.encoder_engine.load_model():
                raise RuntimeError(f"Failed to load encoder: {encoder_path}")
                
            # CTC
            ctc_path = os.path.join(self.model_path, "ctc.onnx")
            if self.config.get('quantized', False):
                quant_path = os.path.join(self.model_path, "ctc.quant.onnx")
                if os.path.exists(quant_path):
                    ctc_path = quant_path
                    
            self.ctc_engine = PooledONNXEngine(ctc_path, engine_config)
            if not await self.ctc_engine.load_model():
                raise RuntimeError(f"Failed to load CTC: {ctc_path}")
                
            # 解码器（可选）
            decoder_path = os.path.join(self.model_path, "decoder.onnx")
            if os.path.exists(decoder_path):
                self.decoder_engine = PooledONNXEngine(decoder_path, engine_config)
                await self.decoder_engine.load_model()
                logger.info("Decoder loaded for attention rescoring")
                
        except Exception as e:
            logger.error(f"Failed to initialize ONNX engines: {e}")
            raise
            
    def _init_encoder_cache(self):
        """初始化编码器缓存"""
        try:
            # 从配置中获取模型参数
            num_blocks = self.config.get('num_blocks', 12)
            head = self.config.get('head', 8)
            output_size = self.config.get('output_size', 512)
            cnn_module_kernel = self.config.get('cnn_module_kernel', 15)
            batch_size = 1
            
            # 注意力缓存
            self.att_cache = torch.zeros((
                num_blocks,
                head,
                self.required_cache_size,
                output_size // head * 2
            ))
            
            # 注意力掩码
            self.att_mask = torch.ones((
                batch_size,
                1,
                self.required_cache_size + self.chunk_size
            ), dtype=torch.bool)
            self.att_mask[:, :, :self.required_cache_size] = 0
            
            # CNN缓存
            self.cnn_cache = torch.zeros((
                num_blocks,
                batch_size,
                output_size,
                cnn_module_kernel - 1
            ))
            
            logger.debug("Encoder cache initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize encoder cache: {e}")
            raise
            
    async def process_audio_chunk(self, audio_data: Union[bytes, np.ndarray], 
                                  is_final: bool = False) -> Dict[str, Any]:
        """
        处理音频块
        
        Args:
            audio_data: 音频数据
            is_final: 是否是最后一个块
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        if not self.is_initialized:
            return {"error": "Engine not initialized", "success": False}

        session_id = getattr(self, 'session_id', 'unknown')
        with PerformanceContext(global_performance_monitor, session_id, "asr_process_chunk") as perf_ctx:
            try:
                start_time = time.time()

                # 提取特征
                if not self.feature_pipeline:
                    return {"error": "Feature pipeline not initialized", "success": False}
                features = self.feature_pipeline.extract_features(audio_data)
            
                # 添加batch维度
                if features.dim() == 2:
                    features = features.unsqueeze(0)  # [1, T, F]

                # 处理特征块
                result = await self._process_feature_chunk(features, is_final)

                # 计算处理时间
                processing_time = (time.time() - start_time) * 1000
                result['processing_time'] = processing_time
                result['chunk_index'] = self.chunk_index

                self.chunk_index += 1

                return result

            except Exception as e:
                perf_ctx.mark_error(str(e))
                return ErrorHandler.handle_and_log_exception(
                    logger, e, ErrorCodes.AUDIO_DECODING_FAILED,
                    context=f"Audio chunk processing (chunk {self.chunk_index})"
                )
            
    async def _process_feature_chunk(self, features: torch.Tensor, 
                                     is_final: bool) -> Dict[str, Any]:
        """处理特征块"""
        try:
            chunk_len = features.size(1)
            
            # 填充右上下文
            if chunk_len < self.right_context:
                pad_len = math.ceil((self.right_context - chunk_len) / 2)
                features = F.pad(features, (0, 0, pad_len, pad_len))
                
            # 编码器推理
            encoder_output = await self._run_encoder(features)
            if encoder_output is None:
                return {"error": "Encoder inference failed", "success": False}
                
            # CTC推理和搜索
            ctc_result = await self._run_ctc_search(encoder_output)
            
            # 生成文本结果
            if is_final:
                text = self._detokenize_with_timestamps(self.ctc_tokens, self.ctc_times)
            else:
                text = self._detokenize_with_timestamps(self.ctc_tokens, [])
                
            self.result_text = text
            
            return {
                "text": text,
                "is_final": is_final,
                "confidence": ctc_result.get("confidence", 0.0),
                "language": self.language,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Feature chunk processing failed: {e}")
            return {"error": str(e), "success": False}
            
    async def _run_encoder(self, chunk: torch.Tensor) -> Optional[torch.Tensor]:
        """运行编码器推理"""
        try:
            # 检查必要的组件
            if not self.encoder_engine:
                logger.error("Encoder engine not initialized")
                return None
            if self.att_cache is None or self.cnn_cache is None:
                logger.error("Encoder cache not initialized")
                return None

            # 准备输入
            inputs = {
                'chunk': chunk.numpy(),
                'offset': np.array(self.offset + 256, dtype=np.int64),
                'required_cache_size': np.array(self.required_cache_size, dtype=np.int64),
                'att_cache': self.att_cache.numpy(),
                'cnn_cache': self.cnn_cache.numpy(),
                'att_mask': self._prepare_att_mask().numpy()
            }

            # 执行推理
            result = await self.encoder_engine.infer(inputs)
            if not result['success']:
                logger.error(f"Encoder inference failed: {result.get('error')}")
                return None
                
            # 解析输出
            encoder_output = result['encoder_output']
            att_cache = result['att_cache']
            cnn_cache = result['cnn_cache']
            
            # 更新缓存
            self.encoder_outputs.append(torch.from_numpy(encoder_output))
            self.att_cache = torch.from_numpy(att_cache)
            self.cnn_cache = torch.from_numpy(cnn_cache)
            self.offset += encoder_output.shape[1]
            
            # 实现滑动窗口，防止内存泄漏
            if len(self.encoder_outputs) > self.max_encoder_outputs:
                # 保留最近的输出，丢弃旧的
                self.encoder_outputs = self.encoder_outputs[-self.max_encoder_outputs:]

            # 返回累积输出
            accum_output = torch.cat(self.encoder_outputs, dim=1)
            return accum_output
            
        except Exception as e:
            logger.error(f"Encoder inference failed: {e}")
            return None

    def _prepare_att_mask(self) -> torch.Tensor:
        """准备注意力掩码"""
        if self.att_mask is None:
            raise RuntimeError("Attention mask not initialized")
        cache_t1 = min(self.required_cache_size, self.offset)
        att_mask = self.att_mask.clone()
        att_mask[:, :, -(cache_t1 + self.chunk_size):] = 1
        return att_mask

    async def _run_ctc_search(self, encoder_output: torch.Tensor) -> Dict[str, Any]:
        """运行CTC推理和搜索"""
        try:
            # 从上次CTC偏移开始，但要确保不超出范围
            ctc_start_offset = min(self.ctc_offset, encoder_output.size(1))
            encoder_output = encoder_output[:, ctc_start_offset:, :]

            if encoder_output.size(1) == 0:
                return {"confidence": 0.0}

            # CTC推理
            if not self.ctc_engine:
                logger.error("CTC engine not initialized")
                return {"confidence": 0.0}

            inputs = {'hidden': encoder_output.numpy()}
            result = await self.ctc_engine.infer(inputs)

            if not result['success']:
                logger.error(f"CTC inference failed: {result.get('error')}")
                return {"confidence": 0.0}

            ctc_probs = torch.from_numpy(result['ctc_output'])

            # CTC前缀束搜索
            tokens, times = self._ctc_prefix_beam_search(ctc_probs)

            # 更新累积结果
            self._update_ctc_results(tokens, times)

            # 计算置信度
            confidence = float(torch.mean(torch.max(ctc_probs, dim=-1)[0]))

            return {"confidence": confidence}

        except Exception as e:
            logger.error(f"CTC search failed: {e}")
            return {"confidence": 0.0}

    def _ctc_prefix_beam_search(self, ctc_probs: torch.Tensor,
                                beam_size: int = 10) -> Tuple[List[int], List[float]]:
        """CTC前缀束搜索（简化版本）"""
        try:
            # 合并当前和缓存的概率
            new_ctc_probs = ctc_probs
            if len(self.ctc_beam_search_cache):
                new_ctc_probs = torch.cat((self.ctc_beam_search_cache[-1], ctc_probs), dim=1)

            # 简化的贪心解码（实际应用中应使用更复杂的束搜索）
            token_ids = torch.argmax(new_ctc_probs[0], dim=-1)

            # 移除重复和空白token
            tokens = []
            times = []
            prev_token = -1

            for i, token_id in enumerate(token_ids):
                token_id = int(token_id)
                if token_id != prev_token and token_id != 0:  # 0是空白token
                    tokens.append(token_id)
                    times.append(float(i))
                prev_token = token_id

            # 保存缓存
            self.ctc_beam_search_cache = [ctc_probs]

            return tokens, times

        except Exception as e:
            logger.error(f"CTC prefix beam search failed: {e}")
            return [], []

    def _update_ctc_results(self, tokens: List[int], times: List[float]):
        """更新CTC累积结果"""
        try:
            if not tokens:
                return

            # 计算绝对时间
            cache_offset = 0
            if len(self.ctc_beam_search_cache) > 1:
                cache_offset = self.ctc_beam_search_cache[-2].size(1)

            abs_times = [t - cache_offset + self.ctc_offset for t in times]

            # 去除上一个chunk的重复结果
            new_tokens = []
            new_times = []

            for i, (token, time) in enumerate(zip(tokens, abs_times)):
                if time not in self.last_chunk_times:
                    new_tokens.append(token)
                    new_times.append(time)

            # 更新累积结果
            self.ctc_tokens.extend(new_tokens)
            self.ctc_times.extend(new_times)
            self.last_chunk_tokens = new_tokens
            self.last_chunk_times = new_times

            # 更新偏移
            self.ctc_offset += len(tokens)

            # 去重处理
            self._deduplicate_tokens()

        except Exception as e:
            logger.error(f"Failed to update CTC results: {e}")

    def _deduplicate_tokens(self):
        """去除重复的token"""
        try:
            if not self.ctc_tokens or not self.ctc_times:
                return

            # 根据时间戳去重
            tuples = [(self.ctc_tokens[0], self.ctc_times[0])]

            for i in range(1, len(self.ctc_tokens)):
                token = self.ctc_tokens[i]
                time = self.ctc_times[i]

                # 如果前后token相同且时间间隔小于阈值，跳过
                if (token == self.ctc_tokens[i-1] and
                    time - self.ctc_times[i-1] <= 3):
                    continue

                # 如果已存在相同的(token, time)，跳过
                if (token, time) in tuples:
                    continue

                tuples.append((token, time))

            # 更新结果
            self.ctc_tokens = [t[0] for t in tuples]
            self.ctc_times = [t[1] for t in tuples]

        except Exception as e:
            logger.error(f"Token deduplication failed: {e}")

    def _detokenize_with_timestamps(self, tokens: List[int],
                                    times: List[float]) -> str:
        """根据时间戳添加间隔并转换为文本"""
        try:
            if not tokens:
                return ""

            if not self.symbol_table:
                return " ".join(str(t) for t in tokens)

            # 转换token为字符
            chars = self.symbol_table.ids2tokens(tokens)

            if len(times) > 0:
                # 根据时间间隔添加空格
                result = []
                time_scale = self.subsampling_rate * 0.04  # 帧到秒的转换
                blank_interval = self.config.get('blank_interval', 0.5)

                times_sec = [t * time_scale for t in times]

                for i in range(len(chars) - 1):
                    result.append(chars[i])
                    if times_sec[i+1] - times_sec[i] > blank_interval:
                        result.append(' ')

                result.append(chars[-1])
                text = "".join(result)
            else:
                text = "".join(chars)

            # 应用字符映射
            text = self.symbol_table.char_map(text)

            return text

        except Exception as e:
            logger.error(f"Detokenization failed: {e}")
            return ""

    async def finalize(self) -> Dict[str, Any]:
        """完成识别，返回最终结果"""
        try:
            final_text = self._detokenize_with_timestamps(self.ctc_tokens, self.ctc_times)

            result = {
                "text": final_text,
                "language": self.language,
                "total_chunks": self.chunk_index,
                "success": True
            }

            # 重置状态
            self.reset()

            return result

        except Exception as e:
            logger.error(f"Finalization failed: {e}")
            return {"error": str(e), "success": False}

    async def cleanup(self):
        """清理资源"""
        try:
            if self.encoder_engine:
                await self.encoder_engine.unload_model()
                self.encoder_engine = None

            if self.ctc_engine:
                await self.ctc_engine.unload_model()
                self.ctc_engine = None

            if self.decoder_engine:
                await self.decoder_engine.unload_model()
                self.decoder_engine = None

            self.feature_pipeline = None
            self.symbol_table = None
            self.is_initialized = False

            logger.info("StreamingASREngine cleaned up")

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

    def is_ready(self) -> bool:
        """检查引擎是否就绪"""
        return self.is_initialized

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "language": self.language,
            "chunk_count": self.chunk_index,
            "token_count": len(self.ctc_tokens),
            "is_ready": self.is_ready()
        }
